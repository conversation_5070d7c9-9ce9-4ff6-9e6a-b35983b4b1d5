from tortoise.models import Model
from tortoise import fields
from typing import Optional, List
from .recording import Recording
from .appointment import Appointment
from .livekit import Room
import uuid


class Customer(Model):
    """Customer/client information"""
    id = fields.CharField(max_length=50, pk=True)
    phone_number = fields.Char<PERSON>ield(max_length=20, unique=True)
    gender = fields.CharField(max_length=10, null=True)
    dob = fields.DateField(null=True)
    add1 = fields.CharField(max_length=255, null=True)
    add2 = fields.CharField(max_length=255, null=True)
    street = fields.CharField(max_length=255, null=True)
    city = fields.CharField(max_length=100, null=True)
    state = fields.CharField(max_length=100, null=True)
    pincode = fields.CharField(max_length=20, null=True)
    email = fields.CharField(max_length=255, null=True)
    preferred_contact_method = fields.CharField(max_length=10, default="phone")
    communication_consent = fields.BooleanField(default=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    firstname = fields.CharField(max_length=255, null=True)
    lastname = fields.CharField(max_length=255, null=True)
    additional_info = fields.TextField(null=True)
    is_transferred = fields.BooleanField(null=True, default=False)

    # Reverse relations
    appointments: fields.ReverseRelation["Appointment"]
    customer_notes: fields.ReverseRelation["CustomerNote"]
    customer_preference: fields.ReverseRelation["CustomerPreference"]
    customer_value: fields.ReverseRelation["CustomerValue"]
    recordings: fields.ReverseRelation["Recording"]
    rooms: fields.ReverseRelation["Room"]
    
    class Meta:
        table = "customers"

    @property
    def full_name(self):
        if self.firstname and self.lastname:
            return f"{self.firstname} {self.lastname}"
        elif self.firstname:
            return self.firstname
        return "N/A"


    def __str__(self):
        return f"{self.firstname} - {self.phone_number}, id={self.id}"

    @classmethod
    async def find_by_phone(cls, phone_number: str) -> Optional["Customer"]:
        """Find customer by phone number"""
        return await cls.filter(phone_number=phone_number).first()

    @classmethod
    async def create_with_data(cls, **data) -> "Customer":
        """Create customer with data"""
        return await cls.create(**data)

    async def get_appointments(self) -> List["Appointment"]:
        """Get customer appointments"""
        return await self.appointments.all().order_by("-appointment_date")


class CustomerValue(Model):
    """Customer value metrics"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    last_visit = fields.DateField(null=True)
    points = fields.IntField(null=True)
    total_spend = fields.DecimalField(max_digits=10, decimal_places=2, null=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Foreign key relations
    customer = fields.OneToOneField(
        "models.Customer", 
        related_name="customer_value",
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "customer_value"

    def __str__(self):
        return f"Value for {self.customer.full_name if self.customer else 'N/A'}"


class CustomerPreference(Model):
    """Customer preferences"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    preferred_location = fields.CharField(max_length=255, null=True)
    visit_frequency = fields.CharField(max_length=100, null=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Foreign key relations
    preferred_staff = fields.ForeignKeyField(
        "models.Staff", 
        related_name="customer_preferences", 
        null=True,
        on_delete=fields.SET_NULL
    )
    customer = fields.OneToOneField(
        "models.Customer", 
        related_name="customer_preference",
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "customer_preference"

    def __str__(self):
        return f"Preferences for {self.customer.full_name if self.customer else 'N/A'}"


class CustomerNote(Model):
    """Notes about customers"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    notes = fields.TextField()
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Foreign key relations
    customer = fields.ForeignKeyField(
        "models.Customer", 
        related_name="customer_notes",
        on_delete=fields.CASCADE
    )
    created_by = fields.ForeignKeyField(
        "models.Staff", 
        related_name="customer_notes", 
        null=True,
        on_delete=fields.CASCADE
    )

    class Meta:
        table = "customer_notes"
        
    def __str__(self):
        return f"Note for {self.customer.full_name if self.customer else 'N/A'}"